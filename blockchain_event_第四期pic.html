<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>区块链技术课程活动封面</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;600;700;800&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
                opacity: 0.3;
            }
            50% {
                transform: scale(1.1);
                opacity: 0.5;
            }
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: #f0f2f5;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .cover {
            width: 1080px;
            height: 640px;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 25%, #0f3460 50%, #e94560 100%);
            position: relative;
            overflow: hidden;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
        }
        
        /* 背景装饰元素 */
        .bg-decoration {
            position: absolute;
            width: 100%;
            height: 100%;
        }
        
        .circle1 {
            position: absolute;
            width: 300px;
            height: 300px;
            background: radial-gradient(circle, rgba(0, 212, 255, 0.3) 0%, transparent 70%);
            border-radius: 50%;
            top: -100px;
            right: -100px;
            animation: pulse 4s ease-in-out infinite;
        }
        
        .circle2 {
            position: absolute;
            width: 200px;
            height: 200px;
            background: radial-gradient(circle, rgba(91, 115, 232, 0.4) 0%, transparent 70%);
            border-radius: 50%;
            bottom: -50px;
            left: -50px;
            animation: pulse 4s ease-in-out infinite reverse;
        }
        
        .grid-pattern {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
            background-size: 50px 50px;
        }
        
        /* 主要内容区域 */
        .content {
            position: relative;
            z-index: 10;
            padding: 80px 60px;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        .header-section {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }
        
        .top-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            margin-bottom: 30px;
        }
        
        .series-badge {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 12px 24px;
            border-radius: 30px;
            color: white;
            font-size: 16px;
            font-weight: 500;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
        }
        
        .course-number {
            background: linear-gradient(45deg, #ff6b9d, #ffa726);
            color: white;
            padding: 8px 20px;
            border-radius: 20px;
            font-size: 18px;
            font-weight: 700;
            display: inline-block;
            box-shadow: 0 8px 25px rgba(255, 107, 157, 0.4);
            letter-spacing: 1px;
        }
        
        .main-title {
            font-size: 48px;
            font-weight: 800;
            line-height: 1.2;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            max-width: 700px;
        }
        
        .highlight-text {
            background: linear-gradient(135deg, #00d4ff, #5b73e8, #ff6b9d);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .subtitle {
            font-size: 22px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 400;
            margin-bottom: 40px;
            max-width: 600px;
            line-height: 1.4;
        }
        
        .key-topics {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            margin-bottom: 40px;
        }
        
        .topic-tag {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 10px 18px;
            border-radius: 15px;
            color: white;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .topic-tag:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: translateY(-2px);
        }
        
        .footer-section {
            display: flex;
            justify-content: space-between;
            align-items: end;
            margin-top: auto;
        }
        
        .event-info {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        
        .info-item {
            display: flex;
            align-items: center;
            gap: 12px;
            color: white;
            font-size: 18px;
            font-weight: 500;
        }
        
        .info-icon {
            width: 24px;
            height: 24px;
            background: linear-gradient(135deg, #00d4ff, #5b73e8);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        }
        
        .blockchain-visual {
            position: absolute;
            right: 60px;
            top: 50%;
            transform: translateY(-50%);
            width: 300px;
            height: 200px;
            opacity: 0.1;
        }
        
        .block {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05));
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            position: absolute;
            animation: blockFloat 6s ease-in-out infinite;
        }
        
        .block:nth-child(1) {
            top: 0;
            left: 0;
            animation-delay: 0s;
        }
        
        .block:nth-child(2) {
            top: 0;
            left: 80px;
            animation-delay: -1s;
        }
        
        .block:nth-child(3) {
            top: 0;
            right: 0;
            animation-delay: -2s;
        }
        
        .block:nth-child(4) {
            bottom: 0;
            left: 40px;
            animation-delay: -3s;
        }
        
        .block:nth-child(5) {
            bottom: 0;
            right: 40px;
            animation-delay: -4s;
        }
        
        /* 连接线 */
        .connection {
            position: absolute;
            height: 2px;
            background: linear-gradient(90deg, rgba(255, 255, 255, 0.3), transparent);
            animation: connectionPulse 3s ease-in-out infinite;
        }
        
        .connection1 {
            top: 30px;
            left: 60px;
            width: 20px;
            animation-delay: 0s;
        }
        
        .connection2 {
            top: 30px;
            right: 60px;
            width: 20px;
            animation-delay: -1s;
        }
        
        /* 六边形装饰图案 */
        .hexagon-pattern {
            position: absolute;
            top: 20%;
            right: 10%;
            width: 200px;
            height: 200px;
            opacity: 0.08;
        }
        
        .hexagon {
            width: 30px;
            height: 26px;
            background: linear-gradient(135deg, #00d4ff, #5b73e8);
            position: absolute;
            clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
            animation: hexFloat 8s ease-in-out infinite;
        }
        
        .hexagon:nth-child(1) { top: 0; left: 40px; animation-delay: 0s; }
        .hexagon:nth-child(2) { top: 20px; left: 65px; animation-delay: -1s; }
        .hexagon:nth-child(3) { top: 20px; left: 15px; animation-delay: -2s; }
        .hexagon:nth-child(4) { top: 40px; left: 40px; animation-delay: -3s; }
        .hexagon:nth-child(5) { top: 60px; left: 65px; animation-delay: -4s; }
        .hexagon:nth-child(6) { top: 60px; left: 15px; animation-delay: -5s; }
        .hexagon:nth-child(7) { top: 80px; left: 40px; animation-delay: -6s; }
        
        /* 波浪装饰 */
        .wave-decoration {
            position: absolute;
            bottom: -50px;
            left: -100px;
            width: 300px;
            height: 100px;
            background: linear-gradient(45deg, rgba(233, 69, 96, 0.1), transparent);
            border-radius: 50%;
            transform: rotate(-15deg);
            animation: waveFloat 10s ease-in-out infinite;
        }
        
        /* 圆点装饰图案 */
        .dots-pattern {
            position: absolute;
            top: 15%;
            left: 5%;
            width: 150px;
            height: 150px;
            opacity: 0.06;
        }
        
        .dot {
            width: 8px;
            height: 8px;
            background: linear-gradient(135deg, #ff6b9d, #ffa726);
            border-radius: 50%;
            position: absolute;
            animation: dotPulse 4s ease-in-out infinite;
        }
        
        .dot:nth-child(1) { top: 0; left: 0; animation-delay: 0s; }
        .dot:nth-child(2) { top: 0; left: 25px; animation-delay: -0.5s; }
        .dot:nth-child(3) { top: 0; left: 50px; animation-delay: -1s; }
        .dot:nth-child(4) { top: 0; left: 75px; animation-delay: -1.5s; }
        .dot:nth-child(5) { top: 25px; left: 0; animation-delay: -2s; }
        .dot:nth-child(6) { top: 25px; left: 25px; animation-delay: -2.5s; }
        .dot:nth-child(7) { top: 25px; left: 50px; animation-delay: -3s; }
        .dot:nth-child(8) { top: 25px; left: 75px; animation-delay: -3.5s; }
        .dot:nth-child(9) { top: 50px; left: 0; animation-delay: -1s; }
        .dot:nth-child(10) { top: 50px; left: 25px; animation-delay: -1.5s; }
        .dot:nth-child(11) { top: 50px; left: 50px; animation-delay: -2s; }
        .dot:nth-child(12) { top: 50px; left: 75px; animation-delay: -2.5s; }
        
        @keyframes hexFloat {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
                opacity: 0.08;
            }
            50% {
                transform: translateY(-15px) rotate(180deg);
                opacity: 0.15;
            }
        }
        
        @keyframes waveFloat {
            0%, 100% {
                transform: rotate(-15deg) scale(1);
            }
            50% {
                transform: rotate(-10deg) scale(1.1);
            }
        }
        
        @keyframes dotPulse {
            0%, 100% {
                transform: scale(1);
                opacity: 0.06;
            }
            50% {
                transform: scale(1.5);
                opacity: 0.12;
            }
        }
            0%, 100% {
                transform: scale(1);
                opacity: 0.3;
            }
            50% {
                transform: scale(1.1);
                opacity: 0.5;
            }
        }
        
        @keyframes blockFloat {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
            }
            33% {
                transform: translateY(-10px) rotate(5deg);
            }
            66% {
                transform: translateY(5px) rotate(-3deg);
            }
        }
        
        @keyframes connectionPulse {
            0%, 100% {
                opacity: 0.3;
            }
            50% {
                opacity: 0.8;
            }
        }
        
        /* 响应式调整 */
        @media (max-width: 1200px) {
            .cover {
                transform: scale(0.9);
            }
        }
        
        @media (max-width: 1000px) {
            .cover {
                transform: scale(0.8);
            }
            
            .main-title {
                font-size: 42px;
            }
            
            .subtitle {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="cover">
        <!-- 背景装饰 -->
        <div class="bg-decoration">
            <div class="circle1"></div>
            <div class="circle2"></div>
            <div class="grid-pattern"></div>
        </div>
        
        <!-- 六边形装饰图案 -->
        <div class="hexagon-pattern">
            <div class="hexagon"></div>
            <div class="hexagon"></div>
            <div class="hexagon"></div>
            <div class="hexagon"></div>
            <div class="hexagon"></div>
            <div class="hexagon"></div>
            <div class="hexagon"></div>
        </div>
        
        <!-- 波浪装饰 -->
        <div class="wave-decoration"></div>
        
        <!-- 圆点装饰图案 -->
        <div class="dots-pattern">
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
        </div>
        
        <!-- 区块链视觉元素 -->
        <div class="blockchain-visual">
            <div class="block"></div>
            <div class="block"></div>
            <div class="block"></div>
            <div class="block"></div>
            <div class="block"></div>
            <div class="connection connection1"></div>
            <div class="connection connection2"></div>
        </div>
        
        <!-- 主要内容 -->
        <div class="content">
            <div class="header-section">
                <div class="top-row">
                    <div class="series-badge">
                        <span>🚀</span>
                        区块链技术开发系列课程
                    </div>
                    
                    <div class="course-number">第四期</div>
                </div>
                
                <h1 class="main-title">
                    <span class="highlight-text">UniswapV2/V3</span>简介及差异化分析
                </h1>
                
                <p class="subtitle">
                    探索DeFi核心协议架构 · 掌握AMM自动做市商原理
                </p>
                
                <div class="key-topics">
                    <div class="topic-tag">集中流动性</div>
                    <div class="topic-tag">AMM机制</div>
                    <div class="topic-tag">NFT凭证</div>
                    <div class="topic-tag">多层费率</div>
                </div>
            </div>
            
            <div class="footer-section">
                <div class="event-info">
                    <div class="info-item">
                        <div class="info-icon">📅</div>
                        <span>8月17号 下午2:30</span>
                    </div>
                    <div class="info-item">
                        <div class="info-icon">📍</div>
                        <span>昆仑巢书吧区</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>