<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>区块链技术开发系列课程海报</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .poster {
            width: 400px;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            border-radius: 24px;
            padding: 32px 28px;
            color: white;
            box-shadow: 
                0 20px 40px rgba(0, 0, 0, 0.3),
                0 0 0 1px rgba(255, 255, 255, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }
        
        .poster::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #00d4ff, #5b73e8, #ff6b9d);
        }
        
        .poster::after {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200px;
            height: 200px;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            border-radius: 50%;
        }
        
        .header {
            text-align: center;
            margin-bottom: 24px;
            position: relative;
            z-index: 2;
        }
        
        .series-tag {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            padding: 6px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            margin-bottom: 12px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .course-number {
            background: linear-gradient(45deg, #ff6b9d, #ffa726);
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 16px;
            box-shadow: 0 4px 15px rgba(255, 107, 157, 0.4);
        }
        
        .title {
            font-size: 20px;
            font-weight: 700;
            line-height: 1.3;
            margin-bottom: 8px;
            background: linear-gradient(135deg, #ffffff, #e3f2fd);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .subtitle {
            font-size: 14px;
            opacity: 0.9;
            font-weight: 400;
        }
        
        .content-section {
            margin: 24px 0;
            position: relative;
            z-index: 2;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #e3f2fd;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .section-title::before {
            content: '';
            width: 4px;
            height: 16px;
            background: linear-gradient(135deg, #00d4ff, #5b73e8);
            border-radius: 2px;
        }
        
        .content-list {
            list-style: none;
            space-y: 8px;
        }
        
        .content-item {
            padding: 10px 16px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            font-size: 13px;
            margin-bottom: 8px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .content-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 3px;
            height: 100%;
            background: linear-gradient(135deg, #00d4ff, #5b73e8);
            transform: scaleY(0);
            transition: transform 0.3s ease;
        }
        
        .content-item:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateX(4px);
        }
        
        .content-item:hover::before {
            transform: scaleY(1);
        }
        
        .info-section {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
            border-radius: 16px;
            padding: 20px;
            margin-top: 24px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            z-index: 2;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            font-size: 14px;
        }
        
        .info-row:last-child {
            margin-bottom: 0;
        }
        
        .info-label {
            font-weight: 600;
            color: #e3f2fd;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .info-value {
            font-weight: 500;
            text-align: right;
            color: white;
        }
        
        .icon {
            width: 16px;
            height: 16px;
            opacity: 0.8;
        }
        
        .highlight {
            background: linear-gradient(45deg, #ff6b9d, #ffa726);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 600;
        }
        
        .decorative-elements {
            position: absolute;
            top: 20px;
            left: 20px;
            width: 60px;
            height: 60px;
            opacity: 0.1;
            background: linear-gradient(45deg, #00d4ff, #5b73e8);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }
        
        .decorative-elements:nth-child(2) {
            top: auto;
            bottom: 20px;
            right: 20px;
            left: auto;
            width: 40px;
            height: 40px;
            animation-delay: -3s;
        }
        
        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-10px);
            }
        }
        
        @media (max-width: 480px) {
            .poster {
                width: 350px;
                padding: 24px 20px;
            }
            
            .title {
                font-size: 18px;
            }
            
            .content-item {
                font-size: 12px;
                padding: 8px 14px;
            }
        }
    </style>
</head>
<body>
    <div class="poster">
        <div class="decorative-elements"></div>
        <div class="decorative-elements"></div>
        
        <div class="header">
            <div class="series-tag">区块链技术开发系列课程</div>
            <div class="course-number">第五期</div>
            <h1 class="title">RWA从ERC3643合规标准到案例分析讨论</h1>
            <p class="subtitle">深度解析RWA合规核心协议</p>
        </div>
        
        <div class="content-section">
            <h2 class="section-title">
                <span>📚</span>
                主要内容
            </h2>
            <ul class="content-list">
                <li class="content-item">
                    <strong>一、</strong>ERC3643受监管资产（RWA）代币化合规代币标准详解
                </li>
                <li class="content-item">
                    <strong>二、</strong>ERC3643和ERC20区别
                </li>
                <li class="content-item">
                    <strong>三、</strong>ERC3643在RWA场景下应用
                </li>
                <li class="content-item">
                    <strong>四、</strong>RWA案例分析讨论
                </li>
            </ul>
        </div>
        
        <div class="info-section">
            <div class="info-row">
                <span class="info-label">
                    <span>📅</span>
                    时间
                </span>
                <span class="info-value">8月24号下午2:30</span>
            </div>
            <div class="info-row">
                <span class="info-label">
                    <span>📍</span>
                    地点
                </span>
                <span class="info-value">昆仑巢书吧区</span>
            </div>
            <div class="info-row">
                <span class="info-label">
                    <span>💡</span>
                    形式
                </span>
                <span class="info-value">技术分享 + 实战解析</span>
            </div>
        </div>
    </div>
</body>
</html>